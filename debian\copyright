Format: http://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Contact: Odoo <<EMAIL>>

Files: *
Copyright: Copyright (C) 2004-2015 Odoo SA. (www.odoo.com)
License: LGPL-3+

Files: odoo/tools/appdirs.py
Copyright: 2005-2010 ActiveState Software Inc; 2013 Eddy Petrișor
License: MIT

Files: odoo/tools/lru.py
Copyright: 2003 Josiah Carlson
License: PSF
Comment: Was published by the author without license info on activestate but
         is a part of the pype editor published by the same author under
         the PSF license. See here:
         http://code.activestate.com/recipes/252524-length-limited-o1-lru-cache-implementation/

Files: addons/account_test/__manifest__.py
Copyright: 2011 CCI Connect asbl; Philmer <<EMAIL>>
License: LGPL-3+

Files: addons/base_import/models/odf_ods_reader.py
Copyright: 2011 Marco Conti
License: Apache-2.0

Files: addons/hr_holidays/models/hr_leave*.py
Copyright: 2005-2006 Axelor SARL
License: LGPL-3+

Files: addons/hw_escpos/escpos/*
Copyright: 2014 <PERSON> & <PERSON> Martinez
License: MIT

Files: addons/l10n_ae/*
Copyright: 2014 Tech Receptives
License: LGPL-3+

Files: addons/l10n_at/*
Copyright: 2015 WT-IO-IT GmbH
License: LGPL-3+

Files: addons/l10n_au/*
Copyright: 2015 Willow IT Pty Ltd
License: LGPL-3+

Files: addons/l10n_be_invoice_bba/*
Copyright: 2011 Noviat nv/sa
License: LGPL-3+

Files: addons/l10n_bo/*
Copyright: 2011 Cubic ERP - Teradata SAC
License: LGPL-3+

Files: addons/l10n_br/*
Copyright: 2009 Renato Lima, Akretion
License: LGPL-3+

Files: addons/l10n_ca/*
Copyright: 2010 Savoir-faire Linux
License: LGPL-3+

Files: addons/l10n_cl/*
Copyright: 2019 Blanco Martín & Asociados
License: LGPL-3+

Files: addons/l10n_cn/*
Copyright: 2008-2008 凯源吕鑫 <EMAIL>; 2012-2012 南京盈通 <EMAIL>; 2008-now 开阖软件 <EMAIL>; 2018-now <EMAIL>
License: LGPL-3+

Files: addons/l10n_co/*
Copyright: David Arnold
License: LGPL-3+

Files: addons/l10n_cr/*
Copyright: 2010-TODAY ClearCorp S.A.
License: BSD-2

Files: addons/l10n_dk/*
Copyright: 2018 Odoo House ApS
License: LGPL-3+

Files: addons/l10n_do/*
Copyright: 2016 - Present iterativo, SRL
License: LGPL-3+

Files: addons/l10n_ec/*
Copyright: 2010-2012 Cristian Salamea Gnuthink Software Labs Cia. Ltda
License: LGPL-3+

Files: addons/l10n_et/*
Copyright: 2012 Michael Telahun Makonnen <<EMAIL>>
License: LGPL-3+

Files: addons/l10n_fr/*
Copyright: 2008 JAILLET Simon, CrysaLEAD
License: LGPL-3+

Files: addons/l10n_fr_fec*/*
Copyright: 2013-2015 Akretion
License: LGPL-3+

Files: addons/l10n_gr/*
Copyright: 2009 P. Christeas
License: LGPL-3+

Files: addons/l10n_gt/*
Copyright: 2009-2010 Soluciones Tecnologócias Prisma S.A
License: LGPL-3+

Files: addons/l10n_hn/*
Copyright: 2009-2010 Salvatore J. Trimarchi
License: LGPL-3+

Files: addons/l10n_hr/*
Copyright: 2011- Slobodni programi d.o.o., Zagreb
License: LGPL-3+

Files: addons/l10n_hu/*
Copyright: 2014 InnOpen Group Kft
License: LGPL-3+

Files: addons/l10n_jp/*
Copyright: Quartile Limited
License: LGPL-3+

Files: addons/l10n_lt/*
Copyright: JSC Focusate
License: LGPL-3+

Files: addons/l10n_lu/*
Copyright: 2011 Thamini S.à.R.L; 2011 ADN Consultants S.à.R.L; 2014 ACSONE SA/NV
License: LGPL-3+

Files: addons/l10n_ma/*
Copyright: 2010 kazacube
License: LGPL-3+

Files: addons/l10n_mx/*
Copyright: 2016 Vauxoo (https://www.vauxoo.com) <<EMAIL>>
License: LGPL-3+

Files: addons/l10n_nl/*
Copyright: 2016 Onestein
License: LGPL-3+

Files: addons/l10n_nz/*
Copyright: 2015 Willow IT Pty Ltd
License: LGPL-3+

Files: addons/l10n_pa/*
Copyright: 2011 Cubic ERP, Teradata SAC
License: LGPL-3+

Files: addons/l10n_pl/__manifest__.py
Copyright: 2009 - now Grzegorz Grzelak
License: LGPL-3+

Files: addons/l10n_pt/*
Copyright: 2012 Thinkopen Solutions, Lda
License: LGPL-3+

Files: addons/l10n_ro/*
Copyright: 2015 Tatár Attila; 2019-2020 NextERP Romania; 2015 Forest and Biomass Services Romania; 2011 TOTAL PC SYSTEMS; 2009 Filsystem
License: LGPL-3+

Files: addons/l10n_sg/*
Copyright: 2014 Tech Receptives
License: LGPL-3+

Files: addons/l10n_si/*
Copyright: 2012 Mentis d.o.o., Dravograd
License: LGPL-3+

Files: addons/l10n_syscohada/*
Copyright: 2010-2011 BAAMTU SARL
License: LGPL-3+

Files: addons/l10n_ua/*
Copyright: 2019 Bohdan Lisnenko, ERP Ukraine
License: LGPL-3+

Files: addons/l10n_uk/*
Copyright: 2011 Smartmode LTD
License: LGPL-3+

Files: addons/l10n_vn/*
Copyright: 2009-2013 General Solutions
License: LGPL-3+

Files: addons/l10n_za/*
Copyright: 2017 Paradigm Digital
License: LGPL-3+

Files: addons/payment_sips/*
Copyright: 2015 Eezee-It
License: LGPL-3+

Files: addons/point_of_sale/models/account_bank_statement.py addons/point_of_sale/models/account_journal.py
Copyright: 2004-2008 PC Solutions (<http://pcsol.be>)
License: LGPL-3+

Files: addons/pos_epson_printer/static/lib/epos-2.12.0.js
Copyright: Seiko Epson Corporation 2016 - 2019; 2011 LearnBoost <<EMAIL>>
License: MIT

Files: addons/stock/tests/test_product.py
Copyright: 2015 Camptocamp SA
License: LGPL-3+

Files: addons/web/doc/_themes/*
Copyright: :copyright: Copyright 2010 by Armin Ronacher
License: BSD-3

Files: addons/web/static/lib/ace/ace.js
Copyright: 2010, Ajax.org B.V
License: BSD-3

Files: addons/web/static/lib/bootstrap/* doc/_extensions/odoo_ext/static/style.css doc/_extensions/odoo_ext/static/bootstrap.js
Copyright: 2011-2019 The Bootstrap Authors; 2011-2019 Twitter, Inc
License: MIT

Files: addons/web/static/lib/bootstrap/scss/_custom-forms.scss
Copyright: 2014 Waybury
License: MIT

Files: addons/web/static/lib/es6-promise/*
Copyright: 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors
License: MIT

Files: addons/web/static/lib/fullcalendar/*
Copyright: 2019 Adam Shaw, Microsoft Corporation
License: MIT

Files: addons/web/static/lib/fuzzy-master/*
Copyright: 2012 Matt York
License: MIT

Files: addons/web/static/lib/signature_pad/signature_pad.umd.js
Copyright: 2023 Szymon Nowak
License: MIT

Files: addons/web/static/lib/jquery/*
Copyright: JS Foundation and other contributors; jQuery Foundation and other contributors
License: MIT

Files: addons/web/static/lib/moment/*
Copyright: Tim Wood, Iskren Chernev, Moment.js contributors
License: MIT

Files: addons/web/static/lib/pdfjs/build/*
Copyright: 2019 Mozilla Foundation; 2019 Denis Pushkarev
License: Apache-2.0

Files: addons/web/static/lib/pdfjs/web/*
Copyright: 2012 Mozilla Foundation
License: Apache-2.0

Files: addons/web/static/lib/pdfjs/web/cmaps/*
Copyright: 1990-2009 Adobe Systems Incorporated
License: BSD-3

Files: addons/web/static/lib/popper/*
Copyright: 2016 Federico Zivolo
License: MIT

Files: odoo/odoo/tests/case.py odoo/odoo/tests/result.py odoo/odoo/tests/suite.py
Copyright: (c) 1999-2003 Steve Purcell; (c) 2003-2010 Python Software Foundation
License: PSF

Files: addons/web/static/lib/py.js/*
Copyright: 2012
License: DWTFYW
 DO WHAT THE FUCK YOU WANT TO PUBLIC LICENSE
 Version 2, December 2004
 .
 Copyright (C) 2012
 .
 Everyone is permitted to copy and distribute verbatim or modified
 copies of this license document, and changing it is allowed as long
 as the name is changed.
 .
            DO WHAT THE FUCK YOU WANT TO PUBLIC LICENSE
   TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION
 .
 0. You just DO WHAT THE FUCK YOU WANT TO.

Files: addons/web/static/lib/qunit/*
Copyright: Copyright jQuery Foundation and other contributors
License: MIT

Files: addons/web/static/lib/qunit/qunit-2.9.1.js
Copyright: jQuery Foundation and other contributors; 2006 Google Inc
License: MIT

Files: addons/web/static/lib/qweb/*
Copyright: 2013 Fabien Meghazi
License: MIT

Files: addons/web/static/lib/underscore.string/*
Copyright: Esa-Matti Suuronen, Alexandru Marasteanu
License: MIT

Files: addons/web/static/src/css/reset.min.css
Copyright: 2011 736 Computing Services Limited
License: MIT

Files: addons/web/static/lib/fontawesome/css/font-awesome.css
Copyright: Dave Gandy
License: MIT

Files: addons/web/static/fonts/google/Montserrat/*
Copyright: 2011 The Montserrat Project Authors
License: SIL-Open-Font-License

Files: addons/web/static/fonts/google/Open_Sans/* addons/web/static/fonts/google/Roboto/*
Copyright: Unknown
License: Apache-2.0

Files: addons/web/static/fonts/google/Oswald/*
Copyright: 2016 The Oswald Project Authors
License: SIL-Open-Font-License 

Files: addons/web/static/fonts/google/Raleway/*
Copyright: 2010 Matt McInerney 2011 Pablo Impallari; 2011 Rodrigo Fuenzalida
License: SIL-Open-Font-License

Files: addons/web/static/fonts/lato/*
Copyright: 2010-2011 tyPoland Lukasz Dziedzic
License: SIL-Open-Font-License

Files: addons/website/static/src/libs/zoomodoo/zoomodoo.js
Copyright: 2013 Matt Hinchliffe
License: MIT

Files: addons/web_editor/static/lib/jQuery.transfo.js
Copyright: 2014 Christophe Matthieu
License: MIT

Files: addons/web_editor/static/lib/cropperjs/* addons/web_editor/static/lib/jquery-cropper/*
Copyright: 2015-present Chen Fengyuan
License: MIT

Files: addons/web_editor/static/lib/vkbeautify/vkbeautify.0.99.00.beta.js
Copyright: 2012 Vadim Kiryukhin
License: MIT

Files: addons/web_editor/static/lib/webgl-image-filter/*
Copyright: 2020 Dominic Szablewski
License: MIT

Files: addons/web_editor/static/lib/html2canvas.js
Copyright: 2013 Niklas von Hertzen; 2010 John Resig
License: MIT

Files: addons/website_event_track/static/lib/idb-keyval/idb-keyval.js
Copyright: 2016 Jake Archibald
License: Apache-2.0

Files: addons/website_google_map/static/src/lib/markerclusterer.js
Copyright: 2010 Google Inc
License: Apache-2.0

Files: doc/_extensions/odoo_ext/pygments_override.py
Copyright: The Pygments team
License: BSD-2

Files: doc/_extensions/odoo_ext/static/bootstrap-3.3.6/*
Copyright: 2011-2015 Twitter Inc
License: MIT

Files: doc/_extensions/pyjsdoc/*
Copyright: Unknown
License: Apache-2.0

Files: doc/_extensions/pyjsparser/*
Copyright: 2014-2017 Piotr Dabkowski
License: MIT

Files: odoo/__init__.py
Copyright: 2010-2012 Daniele Varrazzo
License: BSD-3
Comment: This license only applies to the function 'gevent_wait_callback'

Files: odoo/addons/base/tests/test_res_partner_bank.py
Copyright: 2015 ACSONE SA/NV
License: LGPL-3+

Files: odoo/tools/_vendor/sessions.py
Copyright: 2007 Pallets
License: BSD-3

Files: addons/web/static/lib/fontawesome/fonts/*
Copyright: Dave Gandy
License: OFL-1.1
  PREAMBLE
   The goals of the Open Font License (OFL) are to stimulate worldwide
  development of collaborative font projects, to support the font creation
  efforts of academic and linguistic communities, and to provide a free and
  open framework in which fonts may be shared and improved in partnership
  with others.
  .
  The OFL allows the licensed fonts to be used, studied, modified and
  redistributed freely as long as they are not sold by themselves. The
  fonts, including any derivative works, can be bundled, embedded,
  redistributed and/or sold with any software provided that any reserved
  names are not used by derivative works. The fonts and derivatives,
  however, cannot be released under any other type of license. The
  requirement for fonts to remain under this license does not apply
  to any document created using the fonts or their derivatives.
  .
  DEFINITIONS
  "Font Software" refers to the set of files released by the Copyright
  Holder(s) under this license and clearly marked as such. This may
  include source files, build scripts and documentation.
  .
  "Reserved Font Name" refers to any names specified as such after the
  copyright statement(s).
  .
  "Original Version" refers to the collection of Font Software components as
  distributed by the Copyright Holder(s).
  .
  "Modified Version" refers to any derivative made by adding to, deleting,
  or substituting -- in part or in whole -- any of the components of the
  Original Version, by changing formats or by porting the Font Software to a
  new environment.
  .
  "Author" refers to any designer, engineer, programmer, technical
  writer or other person who contributed to the Font Software.
  .
  PERMISSION & CONDITIONS
   Permission is hereby granted, free of charge, to any person obtaining
  a copy of the Font Software, to use, study, copy, merge, embed, modify,
  redistribute, and sell modified and unmodified copies of the Font
  Software, subject to the following conditions:
  .
  1) Neither the Font Software nor any of its individual components,
     in Original or Modified Versions, may be sold by itself.
  .
  2) Original or Modified Versions of the Font Software may be bundled,
     redistributed and/or sold with any software, provided that each copy
     contains the above copyright notice and this license. These can be
     included either as stand-alone text files, human-readable headers or
     in the appropriate machine-readable metadata fields within text or
     binary files as long as those fields can be easily viewed by the user.
  .
  3) No Modified Version of the Font Software may use the Reserved Font
     Name(s) unless explicit written permission is granted by the corresponding
     Copyright Holder. This restriction only applies to the primary font
     name as presented to the users.
  .
  4) The name(s) of the Copyright Holder(s) or the Author(s) of the Font
     Software shall not be used to promote, endorse or advertise any
     Modified Version, except to acknowledge the contribution(s) of the
     Copyright Holder(s) and the Author(s) or with their explicit written
     permission.
  .
  5) The Font Software, modified or unmodified, in part or in whole,
     must be distributed entirely under this license, and must not be
     distributed under any other license. The requirement for fonts to
     remain under this license does not apply to any document created
     using the Font Software.
  .
  TERMINATION
   This license becomes null and void if any of the above conditions are
  not met.
  .
  DISCLAIMER
   THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF
  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT
  OF COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
  INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL
  DAMAGES, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF THE USE OR INABILITY TO USE THE FONT SOFTWARE OR FROM
  OTHER DEALINGS IN THE FONT SOFTWARE.

License: LGPL-3+
 See `/usr/share/common-licenses/LGPL-3'.

License: BSD-2
 Permission to use, copy, modify, and distribute this software for any
 purpose with or without fee is hereby granted, provided that the
 above copyright notice and this permission notice appear in all
 copies.
 .
 THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL
 WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED
 WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE
 AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL
 DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR
 PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 PERFORMANCE OF THIS SOFTWARE.

License: BSD-3
 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:
 .
 1) Redistributions of source code must retain the above copyright
 notice, this list of conditions and the following disclaimer.
 .
 2) Redistributions in binary form must reproduce the above copyright
 notice, this list of conditions and the following disclaimer in the
 documentation and/or other materials provided with the distribution.
 .
 3) Neither the name of the ORGANIZATION nor the names of its
 contributors may be used to endorse or promote products derived from
 this software without specific prior written permission.
 .
 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
 WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 POSSIBILITY OF SUCH DAMAGE.

License: Apache-2.0
 See `/usr/share/common-licenses/Apache-2.0'

License: MIT
 Permission is hereby granted, free of charge, to any person obtaining a
 copy of this software and associated documentation files (the "Software"),
 to deal in the Software without restriction, including without limitation
 the rights to use, copy, modify, merge, publish, distribute, sublicense,
 and/or sell copies of the Software, and to permit persons to whom the
 Software is furnished to do so, subject to the following conditions:
 .
 The above copyright notice and this permission notice shall be included
 in all copies or substantial portions of the Software.
 .
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, 
 TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE 
 SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

License: SIL-Open-Font-License
 PREAMBLE
 The goals of the Open Font License (OFL) are to stimulate worldwide
 development of collaborative font projects, to support the font creation
 efforts of academic and linguistic communities, and to provide a free and
 open framework in which fonts may be shared and improved in partnership
 with others.
 .
 The OFL allows the licensed fonts to be used, studied, modified and
 redistributed freely as long as they are not sold by themselves. The
 fonts, including any derivative works, can be bundled, embedded,
 redistributed and/or sold with any software provided that any reserved
 names are not used by derivative works. The fonts and derivatives,
 however, cannot be released under any other type of license. The
 requirement for fonts to remain under this license does not apply
 to any document created using the fonts or their derivatives.
 .
 DEFINITIONS
 "Font Software" refers to the set of files released by the Copyright
 Holder(s) under this license and clearly marked as such. This may
 include source files, build scripts and documentation.
 .
 "Reserved Font Name" refers to any names specified as such after the
 copyright statement(s).
 .
 "Original Version" refers to the collection of Font Software components as
 distributed by the Copyright Holder(s).
 .
 "Modified Version" refers to any derivative made by adding to, deleting,
 or substituting -- in part or in whole -- any of the components of the
 Original Version, by changing formats or by porting the Font Software to a
 new environment.
 .
 "Author" refers to any designer, engineer, programmer, technical
 writer or other person who contributed to the Font Software.
 .
 PERMISSION & CONDITIONS
 Permission is hereby granted, free of charge, to any person obtaining
 a copy of the Font Software, to use, study, copy, merge, embed, modify,
 redistribute, and sell modified and unmodified copies of the Font
 Software, subject to the following conditions:
 .
 1) Neither the Font Software nor any of its individual components,
 in Original or Modified Versions, may be sold by itself.
 .
 2) Original or Modified Versions of the Font Software may be bundled,
 redistributed and/or sold with any software, provided that each copy
 contains the above copyright notice and this license. These can be
 included either as stand-alone text files, human-readable headers or
 in the appropriate machine-readable metadata fields within text or
 binary files as long as those fields can be easily viewed by the user.
 .
 3) No Modified Version of the Font Software may use the Reserved Font
 Name(s) unless explicit written permission is granted by the corresponding
 Copyright Holder. This restriction only applies to the primary font name as
 presented to the users.
 .
 4) The name(s) of the Copyright Holder(s) or the Author(s) of the Font
 Software shall not be used to promote, endorse or advertise any
 Modified Version, except to acknowledge the contribution(s) of the
 Copyright Holder(s) and the Author(s) or with their explicit written
 permission.
 .
 5) The Font Software, modified or unmodified, in part or in whole,
 must be distributed entirely under this license, and must not be
 distributed under any other license. The requirement for fonts to
 remain under this license does not apply to any document created
 using the Font Software.
 .
 TERMINATION
 This license becomes null and void if any of the above conditions are
 not met.
 .
 DISCLAIMER
 THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF
 MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT
 OF COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL THE
 COPYRIGHT HOLDER BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL
 DAMAGES, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 FROM, OUT OF THE USE OR INABILITY TO USE THE FONT SOFTWARE OR FROM
 OTHER DEALINGS IN THE FONT SOFTWARE.

License: PSF
 A. HISTORY OF THE SOFTWARE
 ==========================
 .
 Python was created in the early 1990s by Guido van Rossum at Stichting
 Mathematisch Centrum (CWI, see http://www.cwi.nl) in the Netherlands
 as a successor of a language called ABC.  Guido remains Python's
 principal author, although it includes many contributions from others.
 .
 In 1995, Guido continued his work on Python at the Corporation for
 National Research Initiatives (CNRI, see http://www.cnri.reston.va.us)
 in Reston, Virginia where he released several versions of the
 software.
 .
 In May 2000, Guido and the Python core development team moved to
 BeOpen.com to form the BeOpen PythonLabs team.  In October of the same
 year, the PythonLabs team moved to Digital Creations (now Zope
 Corporation, see http://www.zope.com).  In 2001, the Python Software
 Foundation (PSF, see http://www.python.org/psf/) was formed, a
 non-profit organization created specifically to own Python-related
 Intellectual Property.  Zope Corporation is a sponsoring member of
 the PSF.
 .
 All Python releases are Open Source (see http://www.opensource.org for
 the Open Source Definition).  Historically, most, but not all, Python
 releases have also been GPL-compatible; the table below summarizes
 the various releases.
 .
     Release         Derived     Year        Owner       GPL-
       from                                compatible? (1)
 .
     0.9.0 thru 1.2              1991-1995   CWI         yes
     1.3 thru 1.5.2  1.2         1995-1999   CNRI        yes
     1.6             1.5.2       2000        CNRI        no
     2.0             1.6         2000        BeOpen.com  no
     1.6.1           1.6         2001        CNRI        yes (2)
     2.1             2.0****.1   2001        PSF         no
     2.0.1           2.0****.1   2001        PSF         yes
     2.1.1           2.1****.1   2001        PSF         yes
     2.2             2.1.1       2001        PSF         yes
     2.1.2           2.1.1       2002        PSF         yes
     2.1.3           2.1.2       2002        PSF         yes
     2.2.1           2.2         2002        PSF         yes
     2.2.2           2.2.1       2002        PSF         yes
     2.2.3           2.2.2       2003        PSF         yes
     2.3             2.2.2       2002-2003   PSF         yes
     2.3.1           2.3         2002-2003   PSF         yes
     2.3.2           2.3.1       2002-2003   PSF         yes
     2.3.3           2.3.2       2002-2003   PSF         yes
     2.3.4           2.3.3       2004        PSF         yes
     2.3.5           2.3.4       2005        PSF         yes
     2.4             2.3         2004        PSF         yes
     2.4.1           2.4         2005        PSF         yes
     2.4.2           2.4.1       2005        PSF         yes
     2.4.3           2.4.2       2006        PSF         yes
     2.4.4           2.4.3       2006        PSF         yes
     2.5             2.4         2006        PSF         yes
     2.5.1           2.5         2007        PSF         yes
     2.5.2           2.5.1       2008        PSF         yes
     2.5.3           2.5.2       2008        PSF         yes
     2.6             2.5         2008        PSF         yes
     2.6.1           2.6         2008        PSF         yes
     2.6.2           2.6.1       2009        PSF         yes
     2.6.3           2.6.2       2009        PSF         yes
     2.6.4           2.6.3       2009        PSF         yes
     3.0             2.6         2008        PSF         yes
     3.0.1           3.0         2009        PSF         yes
     3.1             3.0.1       2009        PSF         yes
     3.1.1           3.1         2009        PSF         yes
 .
 Footnotes:
 .
 (1) GPL-compatible doesn't mean that we're distributing Python under
     the GPL.  All Python licenses, unlike the GPL, let you distribute
     a modified version without making your changes open source.  The
     GPL-compatible licenses make it possible to combine Python with
     other software that is released under the GPL; the others don't.
 .
 (2) According to Richard Stallman, 1.6.1 is not GPL-compatible,
     because its license has a choice of law clause.  According to
     CNRI, however, Stallman's lawyer has told CNRI's lawyer that 1.6.1
     is "not incompatible" with the GPL.
 .
 Thanks to the many outside volunteers who have worked under Guido's
 direction to make these releases possible.
 .
 .
 B. TERMS AND CONDITIONS FOR ACCESSING OR OTHERWISE USING PYTHON
 ===============================================================
 .
 PYTHON SOFTWARE FOUNDATION LICENSE VERSION 2
 --------------------------------------------
 .
 1. This LICENSE AGREEMENT is between the Python Software Foundation
 ("PSF"), and the Individual or Organization ("Licensee") accessing and
 otherwise using this software ("Python") in source or binary form and
 its associated documentation.
 .
 2. Subject to the terms and conditions of this License Agreement, PSF hereby
 grants Licensee a nonexclusive, royalty-free, world-wide license to reproduce,
 analyze, test, perform and/or display publicly, prepare derivative works,
 distribute, and otherwise use Python alone or in any derivative version,
 provided, however, that PSF's License Agreement and PSF's notice of copyright,
 i.e., "Copyright (c) 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010
 Python Software Foundation; All Rights Reserved" are retained in Python alone or
 in any derivative version prepared by Licensee.
 .
 3. In the event Licensee prepares a derivative work that is based on
 or incorporates Python or any part thereof, and wants to make
 the derivative work available to others as provided herein, then
 Licensee hereby agrees to include in any such work a brief summary of
 the changes made to Python.
 .
 4. PSF is making Python available to Licensee on an "AS IS"
 basis.  PSF MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
 IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, PSF MAKES NO AND
 DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
 FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON WILL NOT
 INFRINGE ANY THIRD PARTY RIGHTS.
 .
 5. PSF SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON
 FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS
 A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON,
 OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.
 .
 6. This License Agreement will automatically terminate upon a material
 breach of its terms and conditions.
 .
 7. Nothing in this License Agreement shall be deemed to create any
 relationship of agency, partnership, or joint venture between PSF and
 Licensee.  This License Agreement does not grant permission to use PSF
 trademarks or trade name in a trademark sense to endorse or promote
 products or services of Licensee, or any third party.
 .
 8. By copying, installing or otherwise using Python, Licensee
 agrees to be bound by the terms and conditions of this License
 Agreement.
 .
 .
 BEOPEN.COM LICENSE AGREEMENT FOR PYTHON 2.0
 -------------------------------------------
 .
 BEOPEN PYTHON OPEN SOURCE LICENSE AGREEMENT VERSION 1
 .
 1. This LICENSE AGREEMENT is between BeOpen.com ("BeOpen"), having an
 office at 160 Saratoga Avenue, Santa Clara, CA 95051, and the
 Individual or Organization ("Licensee") accessing and otherwise using
 this software in source or binary form and its associated
 documentation ("the Software").
 .
 2. Subject to the terms and conditions of this BeOpen Python License
 Agreement, BeOpen hereby grants Licensee a non-exclusive,
 royalty-free, world-wide license to reproduce, analyze, test, perform
 and/or display publicly, prepare derivative works, distribute, and
 otherwise use the Software alone or in any derivative version,
 provided, however, that the BeOpen Python License is retained in the
 Software, alone or in any derivative version prepared by Licensee.
 .
 3. BeOpen is making the Software available to Licensee on an "AS IS"
 basis.  BEOPEN MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
 IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, BEOPEN MAKES NO AND
 DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
 FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF THE SOFTWARE WILL NOT
 INFRINGE ANY THIRD PARTY RIGHTS.
 .
 4. BEOPEN SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF THE
 SOFTWARE FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS
 AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THE SOFTWARE, OR ANY
 DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.
 .
 5. This License Agreement will automatically terminate upon a material
 breach of its terms and conditions.
 .
 6. This License Agreement shall be governed by and interpreted in all
 respects by the law of the State of California, excluding conflict of
 law provisions.  Nothing in this License Agreement shall be deemed to
 create any relationship of agency, partnership, or joint venture
 between BeOpen and Licensee.  This License Agreement does not grant
 permission to use BeOpen trademarks or trade names in a trademark
 sense to endorse or promote products or services of Licensee, or any
 third party.  As an exception, the "BeOpen Python" logos available at
 http://www.pythonlabs.com/logos.html may be used according to the
 permissions granted on that web page.
 .
 7. By copying, installing or otherwise using the software, Licensee
 agrees to be bound by the terms and conditions of this License
 Agreement.
 .
 .
 CNRI LICENSE AGREEMENT FOR PYTHON 1.6.1
 ---------------------------------------
 .
 1. This LICENSE AGREEMENT is between the Corporation for National
 Research Initiatives, having an office at 1895 Preston White Drive,
 Reston, VA 20191 ("CNRI"), and the Individual or Organization
 ("Licensee") accessing and otherwise using Python 1.6.1 software in
 source or binary form and its associated documentation.
 .
 2. Subject to the terms and conditions of this License Agreement, CNRI
 hereby grants Licensee a nonexclusive, royalty-free, world-wide
 license to reproduce, analyze, test, perform and/or display publicly,
 prepare derivative works, distribute, and otherwise use Python 1.6.1
 alone or in any derivative version, provided, however, that CNRI's
 License Agreement and CNRI's notice of copyright, i.e., "Copyright (c)
 1995-2001 Corporation for National Research Initiatives; All Rights
 Reserved" are retained in Python 1.6.1 alone or in any derivative
 version prepared by Licensee.  Alternately, in lieu of CNRI's License
 Agreement, Licensee may substitute the following text (omitting the
 quotes): "Python 1.6.1 is made available subject to the terms and
 conditions in CNRI's License Agreement.  This Agreement together with
 Python 1.6.1 may be located on the Internet using the following
 unique, persistent identifier (known as a handle): 1895.22/1013.  This
 Agreement may also be obtained from a proxy server on the Internet
 using the following URL: http://hdl.handle.net/1895.22/1013".
 .
 3. In the event Licensee prepares a derivative work that is based on
 or incorporates Python 1.6.1 or any part thereof, and wants to make
 the derivative work available to others as provided herein, then
 Licensee hereby agrees to include in any such work a brief summary of
 the changes made to Python 1.6.1.
 .
 4. CNRI is making Python 1.6.1 available to Licensee on an "AS IS"
 basis.  CNRI MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
 IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, CNRI MAKES NO AND
 DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
 FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON 1.6.1 WILL NOT
 INFRINGE ANY THIRD PARTY RIGHTS.
 .
 5. CNRI SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON
 1.6.1 FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS
 A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON 1.6.1,
 OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.
 .
 6. This License Agreement will automatically terminate upon a material
 breach of its terms and conditions.
 .
 7. This License Agreement shall be governed by the federal
 intellectual property law of the United States, including without
 limitation the federal copyright law, and, to the extent such
 U.S. federal law does not apply, by the law of the Commonwealth of
 Virginia, excluding Virginia's conflict of law provisions.
 Notwithstanding the foregoing, with regard to derivative works based
 on Python 1.6.1 that incorporate non-separable material that was
 previously distributed under the GNU General Public License (GPL), the
 law of the Commonwealth of Virginia shall govern this License
 Agreement only as to issues arising under or with respect to
 Paragraphs 4, 5, and 7 of this License Agreement.  Nothing in this
 License Agreement shall be deemed to create any relationship of
 agency, partnership, or joint venture between CNRI and Licensee.  This
 License Agreement does not grant permission to use CNRI trademarks or
 trade name in a trademark sense to endorse or promote products or
 services of Licensee, or any third party.
 .
 8. By clicking on the "ACCEPT" button where indicated, or by copying,
 installing or otherwise using Python 1.6.1, Licensee agrees to be
 bound by the terms and conditions of this License Agreement.
 .
  ACCEPT
 .
 .
 CWI LICENSE AGREEMENT FOR PYTHON 0.9.0 THROUGH 1.2
 --------------------------------------------------
 .
 Copyright (c) 1991 - 1995, Stichting Mathematisch Centrum Amsterdam,
 The Netherlands.  All rights reserved.
 .
 Permission to use, copy, modify, and distribute this software and its
 documentation for any purpose and without fee is hereby granted,
 provided that the above copyright notice appear in all copies and that
 both that copyright notice and this permission notice appear in
 supporting documentation, and that the name of Stichting Mathematisch
 Centrum or CWI not be used in advertising or publicity pertaining to
 distribution of the software without specific, written prior
 permission.
 .
 STICHTING MATHEMATISCH CENTRUM DISCLAIMS ALL WARRANTIES WITH REGARD TO
 THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
 FITNESS, IN NO EVENT SHALL STICHTING MATHEMATISCH CENTRUM BE LIABLE
 FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT
 OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
