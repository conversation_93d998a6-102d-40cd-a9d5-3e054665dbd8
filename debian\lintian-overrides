# correct spelling
odoo: spelling-error-in-copyright Ang And
odoo: spelling-error-in-copyright Filsystem Filesystem

# placeholder test.external.link
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/test_assetsbundle/views/views.xml [<link rel="stylesheet" href="http://test.external.link/style1.css"/>] (http://test.external.link/style1.css)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/test_assetsbundle/views/views.xml [<link rel="stylesheet" href="http://test.external.link/style2.css"/>] (http://test.external.link/style2.css)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/test_assetsbundle/views/views.xml [<script type="text/javascript" src="http://test.external.link/javascript1.js">] (http://test.external.link/javascript1.js)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/test_assetsbundle/views/views.xml [<script type="text/javascript" src="http://test.external.link/javascript2.js">] (http://test.external.link/javascript2.js)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/website/tests/template_qweb_test.xml [<img src="http://test.external.link/img.png"/>] (http://test.external.link/img.png)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/website/tests/template_qweb_test.xml [<link rel="stylesheet" href="http://test.external.link/style1.css"/>] (http://test.external.link/style1.css)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/website/tests/template_qweb_test.xml [<link rel="stylesheet" href="http://test.external.link/style2.css"/>] (http://test.external.link/style2.css)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/website/tests/template_qweb_test.xml [<script type="text/javascript" src="http://test.external.link/javascript1.js">] (http://test.external.link/javascript1.js)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/website/tests/template_qweb_test.xml [<script type="text/javascript" src="http://test.external.link/javascript2.js">] (http://test.external.link/javascript2.js)

# false positive: this is SIL, as per debian/copyright:396
odoo: truetype-font-prohibits-installable-embedding [preview/print only] usr/lib/python3/dist-packages/odoo/addons/web/static/fonts/lato/Lato-BlaIta-webfont.ttf
odoo: truetype-font-prohibits-installable-embedding [preview/print only] usr/lib/python3/dist-packages/odoo/addons/web/static/fonts/lato/Lato-BolIta-webfont.ttf
odoo: truetype-font-prohibits-installable-embedding [preview/print only] usr/lib/python3/dist-packages/odoo/addons/web/static/fonts/lato/Lato-HaiIta-webfont.ttf
odoo: truetype-font-prohibits-installable-embedding [preview/print only] usr/lib/python3/dist-packages/odoo/addons/web/static/fonts/lato/Lato-LigIta-webfont.ttf
odoo: truetype-font-prohibits-installable-embedding [preview/print only] usr/lib/python3/dist-packages/odoo/addons/web/static/fonts/lato/Lato-RegIta-webfont.ttf

# only present in emails sent from the "digest" addon, and are not
# served from the odoo instance itself. This addon is only enabled
# when the user explicitely activates it after install.
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/digest/data/digest_data.xml [<img class="download_app" src="https://www.odoo.com/digest/static/src/img/app_store.png" />] (https://www.odoo.com/digest/static/src/img/app_store.png)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/digest/data/digest_data.xml [<img class="download_app" src="https://www.odoo.com/digest/static/src/img/google_play.png" />] (https://www.odoo.com/digest/static/src/img/google_play.png)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/digest/data/digest_data.xml [<img src="https://www.odoo.com/web/image/24717933/odoo-mobile.png" alt="odoo mobile" />] (https://www.odoo.com/web/image/24717933/odoo-mobile.png)

# only present in emails sent from the "event" addon, and are not
# served from the odoo instance itself. This addon is only enabled
# when the user explicitely activates it after install.
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/event/data/email_template_data.xml [<img src="http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7ccolor:0xa5117d%7clabel:%7c${location}" style="vertical-align:bottom; width: 100%;" alt="google maps"/>] (http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7ccolor:0xa5117d%7clabel:%7c${location})

# part of the demo data, and meant to show how to how to use the
# optional "survey" addon. This addon is only enabled when the user
# explicitely activates it after install.
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/survey/data/survey_demo_quiz.xml [<iframe src="//www.youtube.com/embed/7y4t6yv5l1k?autoplay=0&amp;rel=0" frameborder="0" contenteditable="false">] (//www.youtube.com/embed/7y4t6yv5l1k?autoplay=0&amp;rel=0)

# only present in websites created by the user in the "website" and "website_google_map" 
# addons. Those addons are only enabled when the user explicitely
# activates them after install.
# The font preloading is only an optimization, and could be removed in a
# Debian patch if needed.
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/website/views/website_templates.xml [<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin=""/>] (https://fonts.gstatic.com/)
odoo: privacy-breach-generic usr/lib/python3/dist-packages/odoo/addons/website_google_map/views/google_map_templates.xml [<script src="//maps.google.com/maps/api/js">] (//maps.google.com/maps/api/js)
