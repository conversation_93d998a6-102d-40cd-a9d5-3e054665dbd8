# Odoo Development Environment Guide

This guide provides comprehensive instructions for setting up and working with the Odoo development environment.

## Table of Contents

1. [Environment Setup](#environment-setup)
2. [Database Management](#database-management)
3. [Running Odoo](#running-odoo)
4. [Development Workflow](#development-workflow)
5. [Troubleshooting](#troubleshooting)
6. [Advanced Configuration](#advanced-configuration)

## Environment Setup

### System Requirements

- **Python**: 3.8 or higher
- **Docker**: Latest version with Docker Compose
- **Git**: For version control
- **Memory**: At least 4GB RAM recommended
- **Storage**: At least 10GB free space

### Initial Setup

1. **Clone and Navigate**:
   ```bash
   git clone <repository-url>
   cd erp_dev
   ```

2. **Set up Virtual Environment**:
   ```bash
   # Create and configure virtual environment
   python setup_venv.py
   
   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   
   # Linux/macOS:
   source venv/bin/activate
   ```

3. **Verify Installation**:
   ```bash
   # Check Python packages
   pip list | grep -E "(odoo|psycopg2|lxml)"
   
   # Verify Odoo installation
   python odoo-bin --version
   ```

## Database Management

### Starting the Database

The PostgreSQL database runs in a Docker container for easy management:

```bash
# Start the database service
docker-compose up -d db

# Check database status
docker-compose ps db

# View database logs
docker-compose logs db
```

### Database Configuration

Default database settings (configured in `docker-compose.yml`):
- **Host**: localhost
- **Port**: 5432
- **Database**: odoo
- **User**: odoo
- **Password**: odoo

### Database Operations

```bash
# Stop database
docker-compose stop db

# Restart database
docker-compose restart db

# Reset database (WARNING: This deletes all data)
docker-compose down -v
docker-compose up -d db

# Connect to database directly
docker exec -it odoo_db psql -U odoo -d odoo
```

## Running Odoo

### Using the Odoo Runner (Recommended)

The `odoo_runner.py` script provides an interactive development environment:

```bash
# Basic usage
python odoo_runner.py -c odoo.conf

# With additional parameters
python odoo_runner.py -c odoo.conf --dev=reload,qweb,werkzeug,xml

# With specific database
python odoo_runner.py -c odoo.conf -d my_database
```

### Interactive Controls

While Odoo is running, you can use these keyboard shortcuts:

| Key | Action | Description |
|-----|--------|-------------|
| `r` | Restart | Restart the Odoo server |
| `x` | Exit | Exit the runner |
| `s` | Status | Show server status |
| `k` | Kill | Force kill Odoo process |
| `h` | Help | Show help message |
| `Ctrl+C` | Shutdown | Graceful shutdown |

### Direct Odoo Commands

You can also run Odoo directly without the runner:

```bash
# Basic start
python odoo-bin -c odoo.conf

# Initialize database
python odoo-bin -c odoo.conf -d odoo -i base --stop-after-init

# Update modules
python odoo-bin -c odoo.conf -d odoo -u all --stop-after-init

# Install specific modules
python odoo-bin -c odoo.conf -d odoo -i sale,purchase --stop-after-init
```

## Development Workflow

### Daily Development Routine

1. **Start Development Session**:
   ```bash
   # Start database
   docker-compose up -d db
   
   # Activate virtual environment
   source venv/bin/activate  # Linux/macOS
   # or
   venv\Scripts\activate     # Windows
   
   # Start Odoo with runner
   python odoo_runner.py -c odoo.conf
   ```

2. **Make Changes**:
   - Edit Python files, XML views, or other Odoo components
   - Use `r` to restart the server and see changes
   - Check logs in the terminal for any errors

3. **End Development Session**:
   - Use `x` to exit the runner or `Ctrl+C` for graceful shutdown
   - Optionally stop the database: `docker-compose stop db`

### Module Development

```bash
# Create a new module
python odoo-bin scaffold my_module ./addons/

# Install the new module
python odoo-bin -c odoo.conf -d odoo -i my_module --stop-after-init

# Update module after changes
python odoo-bin -c odoo.conf -d odoo -u my_module --stop-after-init
```

### Testing

```bash
# Run tests for a specific module
python odoo-bin -c odoo.conf -d test_db --test-enable --test-tags my_module --stop-after-init

# Run all tests
python odoo-bin -c odoo.conf -d test_db --test-enable --stop-after-init
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**:
   ```bash
   # Check if database is running
   docker-compose ps db
   
   # Restart database
   docker-compose restart db
   ```

2. **Port Already in Use**:
   ```bash
   # Check what's using port 8069
   netstat -tulpn | grep 8069
   
   # Kill process using the port
   sudo kill -9 <PID>
   ```

3. **Module Import Errors**:
   ```bash
   # Check virtual environment is activated
   which python
   
   # Reinstall requirements
   pip install -r requirements.txt
   ```

4. **Permission Errors**:
   ```bash
   # Fix file permissions (Linux/macOS)
   chmod +x odoo-bin
   chmod +x odoo_runner.py
   ```

### Log Analysis

- **Odoo Logs**: Check the terminal output where `odoo_runner.py` is running
- **Database Logs**: `docker-compose logs db`
- **System Logs**: Check system logs for Docker-related issues

### Performance Issues

1. **Slow Startup**:
   - Reduce workers in `odoo.conf` (set to 0 for development)
   - Disable unnecessary modules
   - Use SSD storage if possible

2. **High Memory Usage**:
   - Adjust memory limits in `odoo.conf`
   - Close unnecessary browser tabs
   - Restart Odoo periodically

## Advanced Configuration

### Custom Addons Path

Edit `odoo.conf` to include custom addon directories:

```ini
addons_path = ./odoo/addons,./custom_addons,./third_party_addons
```

### Development Mode Options

```ini
# Enable all development features
dev_mode = reload,qweb,werkzeug,xml

# Individual options:
# reload: Auto-reload Python code
# qweb: Reload QWeb templates
# werkzeug: Enable Werkzeug debugger
# xml: Reload XML files
```

### Database Configuration

```ini
# Multiple database support
db_name = False
list_db = True

# Single database mode
db_name = my_database
list_db = False
```

### Logging Configuration

```ini
# Detailed logging
log_level = debug
log_handler = :DEBUG

# Module-specific logging
log_handler = odoo.addons.my_module:DEBUG
```

## Tips and Best Practices

1. **Use the Interactive Runner**: The `odoo_runner.py` script is the recommended way to run Odoo during development
2. **Keep Database Running**: Leave the Docker database running during development sessions
3. **Regular Restarts**: Use `r` to restart Odoo after making changes
4. **Monitor Logs**: Keep an eye on the terminal output for errors and warnings
5. **Backup Important Data**: Use `docker-compose down -v` carefully as it deletes all data
6. **Virtual Environment**: Always activate the virtual environment before working
7. **Module Updates**: Use `--stop-after-init` flag when installing/updating modules
