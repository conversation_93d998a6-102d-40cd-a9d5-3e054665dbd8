# Odoo

[![Build Status](https://runbot.odoo.com/runbot/badge/flat/1/master.svg)](https://runbot.odoo.com/runbot)
[![Tech Doc](https://img.shields.io/badge/master-docs-875A7B.svg?style=flat&colorA=8F8F8F)](https://www.odoo.com/documentation/master)
[![Help](https://img.shields.io/badge/master-help-875A7B.svg?style=flat&colorA=8F8F8F)](https://www.odoo.com/forum/help-1)
[![Nightly Builds](https://img.shields.io/badge/master-nightly-875A7B.svg?style=flat&colorA=8F8F8F)](https://nightly.odoo.com/)

Odoo is a suite of web based open source business apps.

The main Odoo Apps include an [Open Source CRM](https://www.odoo.com/page/crm),
[Website Builder](https://www.odoo.com/app/website),
[eCommerce](https://www.odoo.com/app/ecommerce),
[Warehouse Management](https://www.odoo.com/app/inventory),
[Project Management](https://www.odoo.com/app/project),
[Billing &amp; Accounting](https://www.odoo.com/app/accounting),
[Point of Sale](https://www.odoo.com/app/point-of-sale-shop),
[Human Resources](https://www.odoo.com/app/employees),
[Marketing](https://www.odoo.com/app/social-marketing),
[Manufacturing](https://www.odoo.com/app/manufacturing),
[...](https://www.odoo.com/)

Odoo Apps can be used as stand-alone applications, but they also integrate seamlessly so you get
a full-featured [Open Source ERP](https://www.odoo.com) when you install several Apps.

## Development Setup

### Prerequisites

- Python 3.8 or higher
- Docker and Docker Compose
- Git

### Quick Start

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd erp_dev
   ```

2. **Set up the database with Docker Compose**:
   ```bash
   docker-compose up -d db
   ```
   This will start a PostgreSQL 16 database with the following credentials:
   - Database: `odoo`
   - User: `odoo`
   - Password: `odoo`
   - Port: `5432`

3. **Set up Python virtual environment**:
   ```bash
   python setup_venv.py
   ```
   This will create a virtual environment and install all required dependencies.

4. **Activate the virtual environment**:
   - **Windows**: `venv\Scripts\activate`
   - **Linux/macOS**: `source venv/bin/activate`

5. **Start Odoo using the interactive runner**:
   ```bash
   python odoo_runner.py -c odoo.conf
   ```

6. **Access Odoo**:
   Open your browser and go to `http://localhost:8069`

### Odoo Runner Controls

The `odoo_runner.py` script provides interactive controls while Odoo is running:

- **`r`** - Restart Odoo server
- **`x`** - Exit the runner
- **`s`** - Show server status
- **`k`** - Force kill Odoo process
- **`h`** - Show help
- **`Ctrl+C`** - Graceful shutdown

### Development Workflow

1. **Start the database**: `docker-compose up -d db`
2. **Activate virtual environment**: `venv\Scripts\activate` (Windows) or `source venv/bin/activate` (Linux/macOS)
3. **Start Odoo**: `python odoo_runner.py -c odoo.conf`
4. **Develop**: Make your changes and use `r` to restart the server
5. **Stop**: Use `x` to exit or `Ctrl+C` for graceful shutdown

### Configuration

The `odoo.conf` file contains development-friendly settings:
- Database connection to Docker PostgreSQL
- Development mode enabled with auto-reload
- Debug logging
- Demo data enabled
- File upload directory: `./filestore`

### Database Management

- **Start database**: `docker-compose up -d db`
- **Stop database**: `docker-compose stop db`
- **Reset database**: `docker-compose down -v && docker-compose up -d db`
- **View logs**: `docker-compose logs db`

## Getting started with Odoo

For a standard installation please follow the [Setup instructions](https://www.odoo.com/documentation/master/administration/install/install.html)
from the documentation.

To learn the software, we recommend the [Odoo eLearning](https://www.odoo.com/slides),
or [Scale-up, the business game](https://www.odoo.com/page/scale-up-business-game).
Developers can start with [the developer tutorials](https://www.odoo.com/documentation/master/developer/howtos.html).

## Security

If you believe you have found a security issue, check our [Responsible Disclosure page](https://www.odoo.com/security-report)
for details and get in touch with us via email.
