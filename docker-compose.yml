services:
  # PostgreSQL 16 Alpine Database
  db:
    image: postgres:16-alpine
    container_name: odoo_db
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - odoo_db_data:/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - odoo_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U odoo -d odoo"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  odoo_db_data:
    driver: local

networks:
  odoo_network:
    driver: bridge