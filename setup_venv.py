#!/usr/bin/env python3
"""
Virtual Environment Setup Script for Odoo Development

This script sets up a Python virtual environment and installs all required dependencies
for Odoo development from requirements.txt.

Usage:
    python setup_venv.py [--venv-name VENV_NAME]

Options:
    --venv-name VENV_NAME    Name of the virtual environment (default: venv)
    --help                   Show this help message
"""

import os
import sys
import subprocess
import argparse
import platform
from pathlib import Path


def run_command(cmd, check=True, shell=False):
    """Run a command and return the result"""
    print(f"🔧 Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    try:
        result = subprocess.run(
            cmd, 
            check=check, 
            shell=shell, 
            capture_output=True, 
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required for Odoo")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        sys.exit(1)
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")


def create_virtual_environment(venv_name):
    """Create a virtual environment"""
    venv_path = Path(venv_name)
    
    if venv_path.exists():
        print(f"⚠️  Virtual environment '{venv_name}' already exists")
        response = input("Do you want to recreate it? (y/N): ").lower().strip()
        if response == 'y':
            print(f"🗑️  Removing existing virtual environment...")
            if platform.system() == "Windows":
                run_command(f'rmdir /s /q "{venv_name}"', shell=True, check=False)
            else:
                run_command(['rm', '-rf', venv_name], check=False)
        else:
            print(f"✅ Using existing virtual environment '{venv_name}'")
            return venv_path
    
    print(f"🏗️  Creating virtual environment '{venv_name}'...")
    run_command([sys.executable, '-m', 'venv', venv_name])
    print(f"✅ Virtual environment '{venv_name}' created successfully")
    
    return venv_path


def get_venv_python(venv_path):
    """Get the path to the Python executable in the virtual environment"""
    if platform.system() == "Windows":
        return venv_path / "Scripts" / "python.exe"
    else:
        return venv_path / "bin" / "python"


def get_venv_pip(venv_path):
    """Get the path to the pip executable in the virtual environment"""
    if platform.system() == "Windows":
        return venv_path / "Scripts" / "pip.exe"
    else:
        return venv_path / "bin" / "pip"


def upgrade_pip(venv_path):
    """Upgrade pip in the virtual environment"""
    pip_path = get_venv_pip(venv_path)
    print("📦 Upgrading pip...")
    run_command([str(pip_path), 'install', '--upgrade', 'pip'])


def install_requirements(venv_path):
    """Install requirements from requirements.txt"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found in current directory")
        sys.exit(1)
    
    pip_path = get_venv_pip(venv_path)
    print("📦 Installing requirements from requirements.txt...")
    print("⏳ This may take several minutes...")
    
    # Install requirements
    run_command([str(pip_path), 'install', '-r', 'requirements.txt'])
    print("✅ Requirements installed successfully")


def print_activation_instructions(venv_name):
    """Print instructions for activating the virtual environment"""
    print("\n" + "="*60)
    print("🎉 VIRTUAL ENVIRONMENT SETUP COMPLETE!")
    print("="*60)
    print("\n📋 To activate the virtual environment:")
    
    if platform.system() == "Windows":
        print(f"   {venv_name}\\Scripts\\activate")
        print("\n📋 To deactivate:")
        print("   deactivate")
        print("\n📋 To run Odoo with the virtual environment:")
        print(f"   {venv_name}\\Scripts\\python odoo_runner.py")
    else:
        print(f"   source {venv_name}/bin/activate")
        print("\n📋 To deactivate:")
        print("   deactivate")
        print("\n📋 To run Odoo with the virtual environment:")
        print(f"   {venv_name}/bin/python odoo_runner.py")
    
    print("\n📋 Or use the odoo_runner.py script directly:")
    print("   python odoo_runner.py")
    print("\n" + "="*60)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Setup virtual environment for Odoo development")
    parser.add_argument('--venv-name', default='venv', help='Name of the virtual environment (default: venv)')
    
    args = parser.parse_args()
    
    print("🚀 Setting up Odoo development environment...")
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Check Python version
    check_python_version()
    
    # Create virtual environment
    venv_path = create_virtual_environment(args.venv_name)
    
    # Upgrade pip
    upgrade_pip(venv_path)
    
    # Install requirements
    install_requirements(venv_path)
    
    # Print activation instructions
    print_activation_instructions(args.venv_name)


if __name__ == "__main__":
    main()
